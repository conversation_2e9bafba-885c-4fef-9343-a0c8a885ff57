import lang from "./lang";

const {
  manageIdea,
} = lang;

export const mobileWidthLimit = 480;
export const tabletWidthLimit = 768;
export const lowResDeskLimit = 1024;
export const highResDeskLimit = 1280;

export const FILE_SIZE_10_MB = 10000000;
export const acceptedImageMimeTypes = [
  "image/jpeg",
  "image/png",
  "image/svg+xml",
  "image/webp",
  "image/gif",
  "image/apng",
  "image/avif",
];
export const agentLoadingStates = manageIdea.promptLoadingStates;

export const emailRegex =
  /^(?!.*\.{2})([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})$/;
export const usernameRegex = /^[A-Za-z0-9_]{4,15}$/;

export const SupabaseTables = {
  Projects: process.env.NEXT_PUBLIC_TABLE_MEDIA_PROJECTS || '',
};

// Platform character limits
export const PLATFORM_CHARACTER_LIMITS = {
  TWITTER: 280,
  TWITTER_PREMIUM: 25000,
  LINKEDIN: 3000,
  INSTAGRAM: 2200,
  FACEBOOK: 63206,
  YOUTUBE: 5000,
} as const;

export const PLATFORM_CANVAS_SIZES = {
  twitter: { 
    width: 1080, 
    height: 1080,
  },
  x: { 
    width: 1080, 
    height: 1080,
  },
  instagram: { 
    width: 1080, 
    height: 1350,
  },
  linkedin: { 
    width: 1200, 
    height: 1200,
  },
  facebook: { 
    width: 1200, 
    height: 628,
  },
  youtube: { 
    width: 1280, 
    height: 720,
  },
  default: { 
    width: 1200, 
    height: 1200,
  },
} as const;

export const tones = [
  "Professional",
  "Gen-Z",
  "Casual",
  "Academic",
  "Mentor",
  "Creative",
];

export const mixpanelToken = process.env.NEXT_PUBLIC_MIXPANEL_TOKEN;
export const isProd = process.env.NEXT_PUBLIC_ROOT_DOMAIN === "mediapilot.app";

export const ImageStyles = [
  {
    option: "none",
    label: "No Style",
  },
  // Genre Styles
  {
    option: "fantasy",
    label: "Fantasy",
  },
  {
    option: "sciFi",
    label: "Sci-Fi",
  },
  {
    option: "horror",
    label: "Horror",
  },
  {
    option: "baroque",
    label: "Baroque",
  },
  {
    option: "matrix",
    label: "Matrix",
  },
  {
    option: "gameOfThrones",
    label: "Game of Thrones",
  },
  {
    option: "pixar",
    label: "Pixar",
  },
  {
    option: "skyrim",
    label: "Skyrim",
  },
  {
    option: "tokusatsu",
    label: "Tokusatsu",
  },
  // Photography Styles
  {
    option: "candidPhotography",
    label: "Candid Photography",
  },
  {
    option: "streetPhotography",
    label: "Street Photography",
  },
  {
    option: "cinematicFilmStill",
    label: "Cinematic Film Still",
  },
  {
    option: "amateurPhotography",
    label: "Amateur Photography",
  },
  {
    option: "portraitPhotography",
    label: "Portrait Photography",
  },
  {
    option: "glamourPhotography",
    label: "Glamour Photography",
  },
  {
    option: "beautyPhotography",
    label: "Beauty Photography",
  },
  {
    option: "fashionPhotography",
    label: "Fashion Photography",
  },
  {
    option: "highkeyPhotography",
    label: "Highkey Photography",
  },
  {
    option: "lowkeyPhotography",
    label: "Lowkey Photography",
  },
  {
    option: "landscapePhotography",
    label: "Landscape Photography",
  },
  {
    option: "sportsPhotography",
    label: "Sports Photography",
  },
  {
    option: "infraredPhotography",
    label: "Infrared Photography",
  },
  {
    option: "dreamlikePhotography",
    label: "Dreamlike Photography",
  },
  {
    option: "filmNegative",
    label: "Film Negative",
  },
  {
    option: "infrared",
    label: "Infrared",
  },
  {
    option: "splitToning",
    label: "Split Toning",
  },
  {
    option: "tiltShiftPhotography",
    label: "Tilt-Shift Photography",
  },

 

  // Classic & Historical Styles
  {
    option: "vintagePosterArt",
    label: "Vintage Poster Art",
  },
  {
    option: "artDeco",
    label: "Art Deco",
  },
  {
    option: "artNouveau",
    label: "Art Nouveau",
  },
  {
    option: "chromolithography",
    label: "Chromolithography",
  },
  {
    option: "fengShui",
    label: "Feng Shui",
  },
  {
    option: "tenebrism",
    label: "Tenebrism",
  },
  {
    option: "ukiyoE",
    label: "Ukiyo-e",
  },
  {
    option: "abstractExpressionism",
    label: "Abstract Expressionism",
  },
  {
    option: "cubism",
    label: "Cubism",
  },
  {
    option: "symbolism",
    label: "Symbolism",
  },
  {
    option: "synthetism",
    label: "Synthetism",
  },
  {
    option: "watercolor",
    label: "Watercolor",
  },

  // Digital & Modern Styles
  {
    option: "eightBit",
    label: "8-Bit",
  },
  {
    option: "animatedGifArt",
    label: "Animated GIF Art",
  },
  {
    option: "cyberpunk",
    label: "Cyberpunk",
  },
  {
    option: "darkSynth",
    label: "Dark Synth",
  },
  {
    option: "diffusion",
    label: "Diffusion",
  },
  {
    option: "digitalAbstract",
    label: "Digital Abstract",
  },
  {
    option: "editorialIllustration",
    label: "Editorial Illustration",
  },
  {
    option: "vaporwave",
    label: "Vaporwave",
  },
  {
    option: "vectorArt",
    label: "Vector Art",
  },
  {
    option: "barcodeArtwork",
    label: "Barcode Artwork",
  },
  {
    option: "ferrofluid",
    label: "Ferrofluid",
  },
  {
    option: "futurism",
    label: "Futurism",
  },
  {
    option: "gothicArt",
    label: "Gothic Art",
  },
  {
    option: "hyperrealism",
    label: "Hyperrealism",
  },
  {
    option: "magicRealism",
    label: "Magic Realism",
  },
  {
    option: "noir",
    label: "Noir",
  },
  {
    option: "popArt",
    label: "Pop Art",
  },
  {
    option: "vectorIllustration",
    label: "Vector Illustration",
  },

  // Illustration & Drawing Styles
  {
    option: "chibi",
    label: "Chibi",
  },
  {
    option: "doodle",
    label: "Doodle",
  },
  {
    option: "claymation",
    label: "Claymation",
  },
  {
    option: "technicalIllustration",
    label: "Technical Illustration",
  },
  {
    option: "typographyArt",
    label: "Typography Art",
  },
  {
    option: "blueprint",
    label: "Blueprint",
  },
  {
    option: "crossHatching",
    label: "Cross Hatching",
  },
  {
    option: "dotMatrixArt",
    label: "Dot Matrix Art",
  },
  {
    option: "engraving",
    label: "Engraving",
  },
  {
    option: "calligraphy",
    label: "Calligraphy",
  },
  {
    option: "pointillism",
    label: "Pointillism",
  },
  {
    option: "rorschachInkblot",
    label: "Rorschach Inkblot",
  },
  {
    option: "stencilArt",
    label: "Stencil Art",
  },
  {
    option: "zentangle",
    label: "Zentangle",
  },

  // Decorative & Collage Styles
  {
    option: "aboriginalDotPainting",
    label: "Aboriginal Dot Painting",
  },
  {
    option: "grisaille",
    label: "Grisaille",
  },
  {
    option: "kaleidoscopic",
    label: "Kaleidoscopic",
  },
  {
    option: "kineticArt",
    label: "Kinetic Art",
  },
  {
    option: "mandala",
    label: "Mandala",
  },
  {
    option: "mosaic",
    label: "Mosaic",
  },
  {
    option: "neuronFlowers",
    label: "Neuron Flowers",
  },
  {
    option: "prismaArt",
    label: "Prisma Art",
  },
  {
    option: "sgraffito",
    label: "Sgraffito",
  },
  {
    option: "tarotCards",
    label: "Tarot Cards",
  },
  {
    option: "collageArt",
    label: "Collage Art",
  },

  // Nature-Inspired & Organic Styles
  {
    option: "bacteriaArt",
    label: "Bacteria Art",
  },
  {
    option: "cymatics",
    label: "Cymatics",
  },
  {
    option: "dmtArtStyle",
    label: "DMT Art Style",
  },
  {
    option: "inkblot",
    label: "Inkblot",
  },
  {
    option: "liquidChrome",
    label: "Liquid Chrome",
  },
  {
    option: "prismatic",
    label: "Prismatic",
  },
  {
    option: "sculpturalArt",
    label: "Sculptural Art",
  },
  {
    option: "wabiSabi",
    label: "Wabi-Sabi",
  },
  {
    option: "glassmorphism",
    label: "Glassmorphism",
  },
  {
    option: "kirigami",
    label: "Kirigami",
  },

  // Optical & Photographic Styles
  {
    option: "diorama",
    label: "Diorama",
  },
  {
    option: "opticalIllusion",
    label: "Optical Illusion",
  },
  {
    option: "shadowArt",
    label: "Shadow Art",
  },
  {
    option: "stereogram",
    label: "Stereogram",
  },
  {
    option: "trompeLoeil",
    label: "Trompe L'oeil",
  },
];

export const tonesSelection = [
  {
    title: "👨 Professional",
    value: "Professional",
  },
  {
    title: "🧑‍💻 Gen-Z",
    value: "Gen-Z",
  },
  {
    title: "🤙 Casual",
    value: "Casual",
  },
  {
    title: "👨‍🏫 Academic",
    value: "Academic",
  },
  {
    title: "🧑‍🏫 Mentor",
    value: "Mentor",
  },
  {
    title: "👨‍🎨 Creative",
    value: "Creative",
  },
];

export const basicPlanLink = 'https://buy.stripe.com/test_fZeg342659J6cqQ3cc'

export const ACCEPTED_FILE_TYPES = [
  'application/pdf',
  'text/markdown',
  'text/plain',
];

export const MAX_FILE_SIZE = 10 * 1024 * 1024;
